{"buildFiles": ["C:\\Flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Flutter\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\source\\flutterbookstore\\android\\app\\.cxx\\Debug\\3j46i0t6\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Flutter\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\source\\flutterbookstore\\android\\app\\.cxx\\Debug\\3j46i0t6\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}