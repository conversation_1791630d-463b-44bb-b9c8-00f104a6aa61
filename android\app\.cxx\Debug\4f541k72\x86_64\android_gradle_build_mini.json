{"buildFiles": ["C:\\Flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Flutter\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\source\\flutterbookstore\\android\\app\\.cxx\\Debug\\4f541k72\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Flutter\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\source\\flutterbookstore\\android\\app\\.cxx\\Debug\\4f541k72\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}