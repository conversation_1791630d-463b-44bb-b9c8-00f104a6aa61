{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Flutter/Android/SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Flutter/Android/SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Flutter/Android/SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/Flutter/Android/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-9014d9db4e6c38e8194c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-92a66f6d54aef657c63e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5cf61ecea50ca32e6ea2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-92a66f6d54aef657c63e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5cf61ecea50ca32e6ea2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9014d9db4e6c38e8194c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}