<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutterbookstore">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>flutterbookstore</title>
  <link rel="manifest" href="manifest.json">
  <script>
    var serviceWorkerVersion = null;
  </script>
  <script src="flutter.js" defer></script>
  <script>
    // PayPal return URL handler
    window.addEventListener('load', function() {
      // Check if this is a PayPal return
      if (window.location.href.includes('flutterbookstore://paypalpay') ||
          window.location.href.includes('flutterbookstore://cancel')) {
        // Call the Dart function to handle the return
        if (window.handlePayPalReturn) {
          window.handlePayPalReturn(window.location.href);
        }
      }

      // Check for PayPal return data in localStorage (from paypal-return.html or paypal-cancel.html)
      const paypalReturnData = localStorage.getItem('paypalReturnData');
      if (paypalReturnData) {
        // Remove the data from localStorage to prevent reprocessing
        localStorage.removeItem('paypalReturnData');

        // Wait for Flutter to initialize and then process the return
        window.flutterReadyCallback = function() {
          if (window.handlePayPalReturn) {
            window.handlePayPalReturn(paypalReturnData);
          }
        };
      }

      // Check for PayPal cancel data
      const paypalCancelData = localStorage.getItem('paypalCancelData');
      if (paypalCancelData) {
        // Remove the data from localStorage
        localStorage.removeItem('paypalCancelData');

        // Wait for Flutter to initialize and then process the cancel
        window.flutterReadyCallback = function() {
          if (window.handlePayPalReturn) {
            window.handlePayPalReturn(paypalCancelData);
          }
        };
      }
    });
  </script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
