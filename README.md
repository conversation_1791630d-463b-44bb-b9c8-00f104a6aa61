# BookStore App

A modern Flutter application for a bookstore, featuring a comprehensive shopping experience including book browsing, cart management, and checkout process.

## Features

- **Book Browsing**: View featured books, browse by category, and search for specific titles or authors.
- **User Authentication**: Login and registration system, with profile management.
- **Shopping Cart**: Add books to cart, update quantities, and remove items.
- **Checkout Process**: Complete checkout with shipping information and payment method selection.
- **Order Confirmation**: Receive order confirmation with order ID and success animation.
- **Responsive Design**: Works seamlessly on various device sizes.

## Getting Started

### Prerequisites

- Flutter SDK (Latest stable version)
- Dart SDK
- Android Studio / VS Code with Flutter plugin
- An emulator or physical device for testing

### Installation

1. Clone the repository
   ```
   git clone https://github.com/yourusername/flutterbookstore.git
   ```

2. Navigate to the project directory
   ```
   cd flutterbookstore
   ```

3. Install dependencies
   ```
   flutter pub get
   ```

4. Run the app
   ```
   flutter run
   ```

## Checkout Process

The app implements a complete checkout flow:

1. **Cart Page**: Review items in your cart, see total price, and proceed to checkout.
2. **Checkout Page**:
   - Displays order summary with total price
   - Form for shipping information (address, city, zip code, phone)
   - Payment method selection (Cash on Delivery, Credit Card, PayPal)
3. **PayPal Checkout**:
   - When PayPal is selected as the payment method, users are redirected to PayPal's secure checkout page
   - After payment is completed, users are redirected back to the app
   - Payment information is securely processed and stored
4. **Order Success Page**:
   - Confirmation of successful order with animation
   - Displays order ID for reference
   - Options to continue shopping or track your order

## API Integration

The app communicates with a Laravel backend API for:
- Book catalog data
- Cart management
- User authentication
- Order processing

The API endpoints are configured in `lib/config/app_config.dart` and can be modified to point to your backend server.

## Services

The app uses several service classes to manage its functionality:

- **ApiService**: Handles communication with the backend API
- **AuthService**: Manages user authentication
- **CartService**: Manages the shopping cart
- **OrderService**: Handles order creation and management

## Configuration

API settings can be configured in `lib/config/app_config.dart`:

```dart
class AppConfig {
  static String apiHost = '*************';
  static int apiPort = 80;
  static String get apiBaseUrl => 'http://$apiHost/api';

  // PayPal Configuration
  static const bool paypalSandboxMode = true; // Set to false for production
  static const String paypalClientId = 'YOUR_PAYPAL_CLIENT_ID';
  static const String paypalClientSecret = 'YOUR_PAYPAL_CLIENT_SECRET';
  // ...
}
```

### PayPal Integration

The app includes PayPal integration for secure online payments:

1. **Configuration**: Set your PayPal Client ID and Secret in `lib/config/app_config.dart`
2. **Sandbox Mode**: For testing, keep `paypalSandboxMode = true` and use PayPal Sandbox credentials
3. **Production**: For live payments, set `paypalSandboxMode = false` and use production credentials

To obtain PayPal API credentials:
1. Create a PayPal Developer account at [developer.paypal.com](https://developer.paypal.com)
2. Create a new app in the PayPal Developer Dashboard
3. Copy the Client ID and Secret to your app configuration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Flutter team for the excellent framework
- All open-source packages used in this project
