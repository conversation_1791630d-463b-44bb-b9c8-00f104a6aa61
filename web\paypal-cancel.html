<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PayPal Payment Cancelled</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      color: #F44336;
    }
    .cancel-icon {
      color: #F44336;
      font-size: 4rem;
      margin-bottom: 1rem;
    }
    .redirect-text {
      margin-top: 1.5rem;
      color: #666;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #F44336;
      animation: spin 1s linear infinite;
      margin: 1rem auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="cancel-icon">✕</div>
    <h1>Payment Cancelled</h1>
    <p>Your PayPal payment has been cancelled.</p>
    <div class="spinner"></div>
    <p class="redirect-text">Returning to the bookstore app...</p>
  </div>

  <script>
    // Store the cancel action in localStorage
    localStorage.setItem('paypalCancelData', 'flutterbookstore://cancel');
    
    // Redirect back to the main app
    setTimeout(() => {
      window.location.href = '/';
    }, 2000);
  </script>
</body>
</html>
